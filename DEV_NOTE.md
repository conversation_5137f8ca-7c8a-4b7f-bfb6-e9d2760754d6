# SmartStrm

## 特性

- 定时索引 webdav 的目录，按照规则生成 strm
- 定时清除 webdav 已删除文件的 strm
- 支持 webhook 触发刷新特定目录的 strm
- 支持监控删除本地 strm 文件事件，反向删除 webdav 文件（可选）

## 参数

```yaml
webdav:
  hostname: http://***********:5244/dav
  login: Cp0204
  password: dwdwqxs333
  scan_path: /drive/quark/video/movie
alist:
  url:
  token:
  scan_path:
strm:
  # 媒体文件后缀
  media_ext: [".mp4", ".mkv"]
  host_prefix:
  save_dir: "./strm"
```

## 技术原理

核心功能

运行一段 py 代码，历遍指定的 webdav 目录，如果有符合的媒体后缀，则在本地 ./strm 目录同路径下生成 strm 文件

strm的格式为：
```
{hostname}{scan_path}{media_path}
```

使用 Python 语言、依赖：

- Flask 作为 webhook 调用，提供事件触发，提供 WebUI 配置 yaml 参数。
- ruamel.yaml 读写配置
- loging 输出日志
- apscheduler 定时任务
- webdavclient3 文件操作

## 扩展性

webdav、alist 为读取远程文件的驱动，先实现webdav，设计成一个抽象层，可自行扩展。

##

## 其他

以下内容仅做开发者笔记记录，可忽略。

Alist 接口速度测试

| 测试接口     | 时间    |
| ------------ | ------- |
| /api/fs/list | 0.5270s |
| /dav         | 0.0654s |

## Webhook

```
http://127.0.0.1:5000/webhook/9dfb5769ad483e83
```


触发运行一个任务：

```bash
curl -X POST http://your-server/webhook/token \
  -H "Content-Type: application/json" \
  -d '{
    "type": "a_task",
    "task": {
      "name":"movie",
      "driver_path": "/drive/quark/video/movie",
    },
    "strm_config": {
      "media_ext": ["mp4", "mkv"],
      "save_dir": "/strm/temp",
      "url_encode": true,
      "media_size": 100,
      "copy_ext": ["srt", "ass"]
    }
  }'
```

## Emby 删除 strm

接受删除事件

人工删除：

```json
{
  "Title": "从 cNAS 中删除了 冰下的鱼.mp4",
  "Description": "2025 年 5 月 13 日星期二 下午 1:38",
  "Date": "2025-05-13T05:38:45.4633525Z",
  "Event": "library.deleted",
  "Item": {
    "Name": "冰下的鱼.mp4",
    "ServerId": "1f0c69410b2b401f9f7a0e71711e9074",
    "Id": "40847",
    "DateCreated": "2024-08-31T12:47:17.0000000Z",
    "Container": "mp4",
    "SortName": "冰下的鱼.mp4",
    "ExternalUrls": [],
    "Path": "/strm/movie/冰下的鱼.mp4.strm",
    "Taglines": [],
    "Genres": [],
    "RunTimeTicks": 57220480000,
    "Size": **********,
    "FileName": "冰下的鱼.mp4.strm",
    "Bitrate": 2569224,
    "RemoteTrailers": [],
    "ProviderIds": {},
    "IsFolder": false,
    "ParentId": "4",
    "Type": "Movie",
    "Studios": [],
    "GenreItems": [],
    "TagItems": [],
    "ImageTags": {},
    "BackdropImageTags": [],
    "MediaType": "Video"
  },
  "Server": {
    "Name": "cNAS",
    "Id": "1f0c69410b2b401f9f7a0e71711e9074",
    "Version": "********"
  }
}
```

系统删除：

```json
{
  "Title": "从 cNAS 中删除了 真探",
  "Description": "2025年8月1日星期五 上午12:10\n\n这是一部美国犯罪侦探系列剧，采用多时间线的方式，调查揭示出参与其中的人员的个人和职业秘密，无论是在法律内部还是外部。",
  "Date": "2025-07-31T16:10:09.6961898Z",
  "Event": "library.deleted",
  "Item": {
    "Name": "真探",
    "OriginalTitle": "True Detective",
    "ServerId": "1f0c69410b2b401f9f7a0e71711e9074",
    "Id": "69808",
    "DateCreated": "2024-02-02T14:36:19.0000000Z",
    "SortName": "真探",
    "PremiereDate": "2014-01-12T00:00:00.0000000Z",
    "ExternalUrls": [
      {
        "Name": "IMDb",
        "Url": "https://www.imdb.com/title/tt2356777"
      },
      {
        "Name": "MovieDb",
        "Url": "https://www.themoviedb.org/tv/46648"
      },
      {
        "Name": "TheTVDB",
        "Url": "https://thetvdb.com/?tab=series&id=270633"
      },
      {
        "Name": "Zap2It",
        "Url": "https://tvlistings.zap2it.com/overview.html?programSeriesId=EP01814225"
      },
      {
        "Name": "Trakt",
        "Url": "https://trakt.tv/search/tmdb/46648?id_type=show"
      }
    ],
    "Path": "/strm/tv/真探",
    "OfficialRating": "TV-MA",
    "Overview": "这是一部美国犯罪侦探系列剧，采用多时间线的方式，调查揭示出参与其中的人员的个人和职业秘密，无论是在法律内部还是外部。",
    "Taglines": [],
    "Genres": [],
    "CommunityRating": 8.28,
    "FileName": "真探",
    "ProductionYear": 2014,
    "RemoteTrailers": [],
    "ProviderIds": {
      "Tmdb": "46648",
      "Imdb": "tt2356777",
      "TvRage": "31369",
      "Tvdb": "270633",
      "Facebook": "TrueDetective",
      "Fan Site": "https://true-detective.fandom.com/wiki/True_Detective_Wiki",
      "Instagram": "truedetective",
      "Official Website": "https://www.hbo.com/true-detective",
      "Reddit": "TrueDetective",
      "Zap2It": "EP01814225",
      "TV Maze": "5/true-detective",
      "X (Twitter)": "TrueDetective"
    },
    "IsFolder": true,
    "Type": "Series",
    "Studios": [],
    "GenreItems": [],
    "TagItems": [],
    "Status": "Continuing",
    "AirDays": [],
    "PrimaryImageAspectRatio": 0.6666666666666666,
    "ImageTags": {
      "Primary": "cec49ce39645117a7cc426a0bd8ac68c",
      "Logo": "8e8a3e9f540c75f1fa3dcfed96fa0795"
    },
    "BackdropImageTags": [
      "2376b00828446f3bee630696c0c7912c"
    ]
  },
  "Server": {
    "Name": "cNAS",
    "Id": "1f0c69410b2b401f9f7a0e71711e9074",
    "Version": "********"
  }
}
```


http://127.0.0.1:8097/smartstrm/quark/video/anime/%E5%85%A8%E8%81%8C%E7%8C%8E%E4%BA%BA%20(2011)/S02/Hunter.X.Hunter.2011.S02E136.2160p.DTS-HD.MA2.0.H264.AAC.mp4
http://127.0.0.1:5000/smartstrm/quark/video/anime/%E5%85%A8%E8%81%8C%E7%8C%8E%E4%BA%BA%20(2011)/S02/Hunter.X.Hunter.2011.S02E136.2160p.DTS-HD.MA2.0.H264.AAC.mp4