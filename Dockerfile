# 编译阶段
FROM python:3.13-alpine AS builder

# 设置工作目录
WORKDIR /app

# 复制源代码和构建脚本
COPY . .

# 安装编译依赖
RUN apk update \
    && apk add --no-cache build-base \
    && pip install --no-cache-dir -U cython setuptools \
    && cd build \
    && python build.py build_ext --inplace \
    && cd ..

# 最终阶段
FROM python:3.13-alpine

# 设置工作目录
WORKDIR /app

# 从编译阶段复制编译后的文件
COPY --from=builder /app/build/app /app

# 安装依赖
RUN pip install --no-cache-dir -r requirements.txt

# 时区
ENV TZ="Asia/Shanghai"

# 暴露端口
EXPOSE 8024

# 启动命令
CMD ["python", "run.py"]