name: smartstrm
services:
  smartstrm:
    build: .
    container_name: smartstrm
    restart: unless-stopped
    ports:
      - "8024:8024"
      - "8097:8097"
    volumes:
      - /DATA/AppData/smartstrm/config:/app/config
      - /DATA/AppData/smartstrm/logs:/app/logs
      - /DATA/AppData/smartstrm/strm:/strm
    environment:
      - DEBUG=false
      - ADMIN_USERNAME=admin
      - ADMIN_PASSWORD=admin
