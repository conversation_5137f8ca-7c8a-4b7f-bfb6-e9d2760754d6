Webhook

说明：此功能会将开发者相关的订单打给配置好的url，同时要求开发者返回固定结构，以明确表示成功收到回调。（不排除以后在异常时会重复请求，因此建议做幂等逻辑，支持重复推送）平台请求开发者配置的URL数据示例，目前 data.type 仅为 order ，data.order 对象具体订单字段见最下方解释

```json
{
  "ec": 200,
  "em": "ok",
  "data": {
    "type": "order",
    "order": {
      "out_trade_no": "202106232138371083454010626",
      "custom_order_id": "Steam12345",
      "user_id": "adf397fe8374811eaacee52540025c377",
      "user_private_id":"fdf981fu8f7g891euacee57430321c377",
      "plan_id": "a45353328af911eb973052540025c377",
      "month": 1,
      "total_amount": "5.00",
      "show_amount": "5.00",
      "status": 2,
      "remark": "用户的邮箱",
      "redeem_id": "",
      "product_type": 0,
      "discount": "0.00",
      "sku_detail": [{
        "sku_id": "b082342c4aba11ebb5cb52540025c377",
        "count": 1,
        "name": "15000 赏金/货币 兑换码",
        "album_id": "",
        "pic": "https://pic1.afdiancdn.com/user/8a8e408a3aeb11eab26352540025c377/common/sfsfsff.jpg"
      }],
      "address_person": "",
      "address_phone": "",
      "address_address": ""
    }
  }
}
```

要求开发者响应的JSON示例，如果接口不返回ec 200 ，则平台认为回调失败

```json
{"ec":200,"em":""}
```