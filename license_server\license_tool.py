#!/usr/bin/env python3
"""
许可工具 - 用于生成和验证许可
"""

import os
import sys
import json
import time
import argparse
import requests

def generate_license(server_url: str, email: str, days: int, plan: str = "pro"):
    """生成许可"""
    try:
        print(f"正在为 {email} 生成 {days} 天的 {plan} 许可...")

        response = requests.post(
            f"{server_url}/api/license/generate",
            json={
                "email": email,
                "days": days,
                "plan": plan
            },
            timeout=10
        )

        if response.status_code == 200:
            result = response.json()
            if result.get("code") == 200:
                print("✓ 许可生成成功")
                print(f"邮箱: {result['license_info']['email']}")
                print(f"过期时间: {result['license_info']['expires_at']}")
                print(f"计划: {result['license_info']['plan']}")
                print()
                print("LICENSE环境变量:")
                print(f"export LICENSE='{result['license_env']}'")
                print()
                print("Windows环境变量:")
                print(f"set LICENSE={result['license_env']}")
                return True
            else:
                print(f"✗ 许可生成失败: {result.get('message')}")
                return False
        else:
            print(f"✗ 许可生成请求失败: {response.status_code}")
            return False

    except Exception as e:
        print(f"✗ 生成许可异常: {e}")
        return False

def list_users(server_url: str):
    """列出用户"""
    try:
        print("获取用户列表...")

        response = requests.get(f"{server_url}/api/users", timeout=10)

        if response.status_code == 200:
            result = response.json()
            if result.get("code") == 200:
                users = result.get("users", [])
                print(f"\n共有 {len(users)} 个用户:")
                print("-" * 80)
                print(f"{'邮箱':<30} {'计划':<10} {'过期时间':<20} {'状态':<10}")
                print("-" * 80)
                for user in users:
                    status = "已过期" if user["is_expired"] else "有效"
                    print(f"{user['email']:<30} {user['plan']:<10} {user['expires_at']:<20} {status:<10}")
                print("-" * 80)
                return True
            else:
                print("✗ 获取用户列表失败")
                return False
        else:
            print(f"✗ 获取用户列表请求失败: {response.status_code}")
            return False

    except Exception as e:
        print(f"✗ 获取用户列表异常: {e}")
        return False

def verify_license(server_url: str, license_env: str = None):
    """验证许可"""
    try:
        # 如果没有提供许可，从环境变量获取
        if not license_env:
            license_env = os.getenv("LICENSE")
            if not license_env:
                print("✗ 未找到LICENSE环境变量")
                return False

        print("验证许可...")

        # 设置环境变量
        os.environ["LICENSE"] = license_env

        # 添加src到路径
        sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

        # 导入许可管理器
        from ..src.core.license_manager import LicenseManager

        # 创建许可管理器实例
        license_manager = LicenseManager(server_url)

        # 初始化许可
        if license_manager.initialize():
            print("✓ 许可验证成功")
            print(f"邮箱: {license_manager.get_email()}")
            print(f"过期时间: {license_manager.get_expires_at_formatted()}")

            # 测试功能检查
            if license_manager.is_feature_enabled("pro_feature"):
                print("✓ Pro功能已启用")
            else:
                print("✗ Pro功能未启用")
            return True
        else:
            print("✗ 许可验证失败")
            return False

    except Exception as e:
        print(f"✗ 许可验证异常: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="SmartStrm Pro 许可工具")
    parser.add_argument("--server", default="http://localhost:8099", help="许可服务器URL")

    subparsers = parser.add_subparsers(dest="command", help="可用命令")

    # 生成许可命令
    gen_parser = subparsers.add_parser("generate", help="生成许可")
    gen_parser.add_argument("email", help="用户邮箱")
    gen_parser.add_argument("--days", type=int, default=30, help="有效天数 (默认: 30)")
    gen_parser.add_argument("--plan", default="pro", choices=["basic", "pro", "enterprise"], help="许可计划 (默认: pro)")

    # 列出用户命令
    subparsers.add_parser("list", help="列出所有用户")

    # 验证许可命令
    verify_parser = subparsers.add_parser("verify", help="验证许可")
    verify_parser.add_argument("--license", help="许可字符串 (如果不提供则从环境变量读取)")

    args = parser.parse_args()

    if not args.command:
        parser.print_help()
        return 1

    print("SmartStrm Pro 许可工具")
    print("=" * 50)

    if args.command == "generate":
        success = generate_license(args.server, args.email, args.days, args.plan)
    elif args.command == "list":
        success = list_users(args.server)
    elif args.command == "verify":
        success = verify_license(args.server, args.license)
    else:
        print(f"未知命令: {args.command}")
        return 1

    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
