#!/usr/bin/env python3
"""
许可系统测试脚本
演示完整的许可验证流程
"""

import os
import sys
import json
import time
import requests
import subprocess
import threading
from typing import Dict, Any

# 添加src到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def start_license_server():
    """启动许可服务器"""
    print("启动许可服务器...")
    try:
        # 启动服务器进程
        process = subprocess.Popen([
            sys.executable, "license_server_demo.py"
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        
        # 等待服务器启动
        time.sleep(3)
        
        # 检查服务器是否启动成功
        try:
            response = requests.get("http://localhost:8099/", timeout=5)
            if response.status_code == 200:
                print("✓ 许可服务器启动成功")
                return process
            else:
                print("✗ 许可服务器启动失败")
                return None
        except requests.exceptions.RequestException:
            print("✗ 无法连接到许可服务器")
            return None
            
    except Exception as e:
        print(f"✗ 启动许可服务器失败: {e}")
        return None

def generate_license(email: str, days: int = 30) -> str:
    """生成许可"""
    try:
        print(f"为 {email} 生成 {days} 天许可...")
        
        response = requests.post(
            "http://localhost:8099/api/license/generate",
            json={
                "email": email,
                "days": days,
                "plan": "pro"
            },
            timeout=10
        )
        
        if response.status_code == 200:
            result = response.json()
            if result.get("code") == 200:
                print("✓ 许可生成成功")
                print(f"  邮箱: {result['license_info']['email']}")
                print(f"  过期时间: {result['license_info']['expires_at']}")
                print(f"  计划: {result['license_info']['plan']}")
                return result["license_env"]
            else:
                print(f"✗ 许可生成失败: {result.get('message')}")
                return ""
        else:
            print(f"✗ 许可生成请求失败: {response.status_code}")
            return ""
            
    except Exception as e:
        print(f"✗ 生成许可异常: {e}")
        return ""

def test_license_verification(license_env: str):
    """测试许可验证"""
    try:
        print("\n测试许可验证...")
        
        # 设置环境变量
        os.environ["LICENSE"] = license_env
        
        # 导入许可管理器
        from core.license_manager import LicenseManager
        
        # 创建许可管理器实例
        license_manager = LicenseManager("http://localhost:8099")
        
        # 初始化许可
        if license_manager.initialize():
            print("✓ 许可验证成功")
            
            # 获取许可信息
            license_info = license_manager.get_license_info()
            if license_info:
                print(f"  邮箱: {license_manager.get_email()}")
                print(f"  过期时间: {license_manager.get_expires_at_formatted()}")
                
                # 测试功能检查
                if license_manager.is_feature_enabled("pro_feature"):
                    print("✓ Pro功能已启用")
                else:
                    print("✗ Pro功能未启用")
            else:
                print("✗ 无法获取许可信息")
        else:
            print("✗ 许可验证失败")
            
    except Exception as e:
        print(f"✗ 许可验证异常: {e}")
        import traceback
        traceback.print_exc()

def test_offline_mode(license_env: str):
    """测试离线模式"""
    try:
        print("\n测试离线模式...")
        
        # 设置环境变量
        os.environ["LICENSE"] = license_env
        
        # 导入许可管理器
        from core.license_manager import LicenseManager
        
        # 创建许可管理器实例（使用无效的服务器地址模拟离线）
        license_manager = LicenseManager("http://invalid-server:9999")
        
        # 初始化许可（应该回退到离线模式）
        if license_manager.initialize():
            print("✓ 离线许可验证成功")
            print(f"  邮箱: {license_manager.get_email()}")
            print(f"  过期时间: {license_manager.get_expires_at_formatted()}")
        else:
            print("✗ 离线许可验证失败")
            
    except Exception as e:
        print(f"✗ 离线许可验证异常: {e}")

def list_users():
    """列出用户"""
    try:
        print("\n查看用户列表...")
        
        response = requests.get("http://localhost:8099/api/users", timeout=10)
        
        if response.status_code == 200:
            result = response.json()
            if result.get("code") == 200:
                users = result.get("users", [])
                print(f"共有 {len(users)} 个用户:")
                for user in users:
                    status = "已过期" if user["is_expired"] else "有效"
                    print(f"  - {user['email']} ({user['plan']}) - {user['expires_at']} [{status}]")
            else:
                print("✗ 获取用户列表失败")
        else:
            print(f"✗ 获取用户列表请求失败: {response.status_code}")
            
    except Exception as e:
        print(f"✗ 获取用户列表异常: {e}")

def main():
    """主函数"""
    print("SmartStrm Pro 许可系统测试")
    print("=" * 50)
    
    # 启动许可服务器
    server_process = start_license_server()
    if not server_process:
        print("无法启动许可服务器，退出测试")
        return 1
    
    try:
        # 查看预置用户
        list_users()
        
        # 生成新许可
        test_email = "<EMAIL>"
        license_env = generate_license(test_email, 30)
        
        if license_env:
            # 测试在线许可验证
            test_license_verification(license_env)
            
            # 测试离线模式
            test_offline_mode(license_env)
        
        print("\n" + "=" * 50)
        print("测试完成！")
        print("许可服务器仍在运行，您可以手动测试其他功能")
        print("访问 http://localhost:8099 查看API文档")
        print("按 Ctrl+C 停止服务器")
        
        # 保持服务器运行
        try:
            server_process.wait()
        except KeyboardInterrupt:
            print("\n正在停止服务器...")
            server_process.terminate()
            server_process.wait()
            print("服务器已停止")
        
        return 0
        
    except Exception as e:
        print(f"测试异常: {e}")
        return 1
    finally:
        # 清理环境变量
        if "LICENSE" in os.environ:
            del os.environ["LICENSE"]
        
        # 确保服务器进程被终止
        if server_process and server_process.poll() is None:
            server_process.terminate()
            server_process.wait()

if __name__ == "__main__":
    sys.exit(main())
