import os
import json
import time
import hashlib
import requests
from typing import Dict, Any, Optional
from Crypto.Cipher import AES
from Crypto.PublicKey import RSA
from Crypto.Signature import pkcs1_15
from Crypto.Hash import SHA256
from Crypto.Util.Padding import pad, unpad
import base64
from core.log_manager import LogManager

logger = LogManager.get_logger(__name__)


class LicenseManager:
    """许可管理器"""

    def __init__(self, license_server_url: str = "http://localhost:8099"):
        """初始化许可管理器

        Args:
            license_server_url: 许可服务器URL
        """
        self.license_server_url = license_server_url
        self.secret = "SmartStrm2024SecretKey1234567890AB"  # 32字节密钥
        self.app_name = "smartstrm"
        self.version = "1.0.0"

        # RSA公钥（用于验证服务端签名）
        self.public_key_pem = """-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAw3kabiWhjsgWJBZlnn8y
OIhbl5gzve2zptHSV2XOsFkVuCj1FX/PqNABFRq81rTBlG6Qb5I6BS+LK/s5NH+1
jIIcTs9Jj3NjSPUi7MV112pS3JnRsSIPv/oDYZI+qEscDNhIKsZ0Qe73HlI7i7FB
hErRxNTx2ELSgBKGHQHHgbUcCSs6ZPv76siPLfapgYItWmb5rOM8JnH9+Mr3Z3RF
incuuy1PCzb776Zqr2fpKBptn/Hk6bvdYtaGvqxaCzzqEOYokQzpBkjKPG2sh7u4
r8qA6aTIYq+h5TtjpizlPC0rcyNVwCJ7cg1XNCGeJ2K/j5yOLBf1px3nZKh5ywOp
dQIDAQAB
-----END PUBLIC KEY-----"""

        self.license_info: Optional[Dict[str, Any]] = None
        self.is_valid = False

    def _aes_encrypt(self, data: str) -> str:
        """AES加密"""
        try:
            cipher = AES.new(self.secret.encode()[:32], AES.MODE_CBC)
            padded_data = pad(data.encode(), AES.block_size)
            encrypted = cipher.encrypt(padded_data)
            # 返回 IV + 加密数据的base64编码
            return base64.b64encode(cipher.iv + encrypted).decode()
        except Exception as e:
            logger.error(f"AES加密失败: {e}")
            return ""

    def _aes_decrypt(self, encrypted_data: str) -> str:
        """AES解密"""
        try:
            data = base64.b64decode(encrypted_data)
            iv = data[:16]  # AES block size
            encrypted = data[16:]
            cipher = AES.new(self.secret.encode()[:32], AES.MODE_CBC, iv)
            decrypted = unpad(cipher.decrypt(encrypted), AES.block_size)
            return decrypted.decode()
        except Exception as e:
            logger.error(f"AES解密失败: {e}")
            return ""

    def _verify_rsa_signature(self, data: str, signature: str) -> bool:
        """验证RSA签名"""
        try:
            public_key = RSA.import_key(self.public_key_pem)
            signature_bytes = base64.b64decode(signature)
            hash_obj = SHA256.new(data.encode())
            pkcs1_15.new(public_key).verify(hash_obj, signature_bytes)
            return True
        except Exception as e:
            logger.error(f"RSA签名验证失败: {e}")
            return False

    def _generate_request_signature(self, params: Dict[str, Any]) -> str:
        """生成请求签名"""
        # 按key排序拼接参数
        sorted_params = sorted(params.items())
        param_str = "&".join([f"{k}={v}" for k, v in sorted_params])
        param_str += f"&{self.secret}"
        return hashlib.md5(param_str.encode()).hexdigest()

    def _load_license_from_env(self) -> Optional[Dict[str, Any]]:
        """从环境变量加载许可"""
        try:
            license_env = os.getenv("LICENSE")
            if not license_env:
                logger.warning("未找到LICENSE环境变量")
                return None

            # 解密环境变量中的许可信息
            decrypted = self._aes_decrypt(license_env)
            if not decrypted:
                logger.error("解密LICENSE环境变量失败")
                return None

            license_data = json.loads(decrypted)

            # 验证签名
            if not self._verify_rsa_signature(license_data["license"], license_data["signature"]):
                logger.error("许可签名验证失败")
                return None

            # 解密许可信息
            license_json = self._aes_decrypt(license_data["license"])
            if not license_json:
                logger.error("解密许可信息失败")
                return None

            return json.loads(license_json)
        except Exception as e:
            logger.error(f"加载环境变量许可失败: {e}")
            return None

    def _request_online_license(self, license_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """请求在线许可验证"""
        try:
            # 加密许可信息
            encrypted_license = self._aes_encrypt(json.dumps(license_data))

            # 构建请求参数
            timestamp = int(time.time())
            params = {
                "license": encrypted_license,
                "version": self.version,
                "timestamp": timestamp
            }

            # 生成签名
            signature = self._generate_request_signature(params)
            params["signature"] = signature

            # 发送请求
            response = requests.post(
                f"{self.license_server_url}/api/license/verify",
                json=params,
                timeout=10
            )

            if response.status_code != 200:
                logger.error(f"在线许可验证请求失败: {response.status_code}")
                return None

            result = response.json()
            if result.get("code") != 200:
                logger.error(f"在线许可验证失败: {result}")
                return None

            # 验证返回的签名
            if not self._verify_rsa_signature(result["license"], result["signature"]):
                logger.error("在线许可返回签名验证失败")
                return None

            # 解密返回的许可信息
            license_json = self._aes_decrypt(result["license"])
            if not license_json:
                logger.error("解密在线许可信息失败")
                return None

            return json.loads(license_json)
        except Exception as e:
            logger.error(f"在线许可验证异常: {e}")
            return None

    def _is_license_valid(self, license_data: Dict[str, Any], allow_offline_days: int = 3) -> bool:
        """检查许可是否有效"""
        try:
            current_time = int(time.time())
            expires_at = license_data.get("expires_at", 0)
            updated_at = license_data.get("updated_at", 0)

            # 检查应用标识
            if license_data.get("app") != self.app_name:
                logger.error("许可应用标识不匹配")
                return False

            # 检查是否过期
            if expires_at < current_time:
                logger.error("许可已过期")
                return False

            # 如果是离线模式，检查更新时间
            if allow_offline_days > 0:
                offline_limit = current_time - (allow_offline_days * 24 * 3600)
                if updated_at < offline_limit:
                    logger.error(f"离线许可超过{allow_offline_days}天未更新")
                    return False

            return True
        except Exception as e:
            logger.error(f"许可验证异常: {e}")
            return False

    def initialize(self) -> bool:
        """初始化许可管理器"""
        try:
            # 从环境变量加载许可
            env_license = self._load_license_from_env()
            if not env_license:
                logger.error("无法加载许可信息")
                return False

            # 尝试在线验证
            online_license = self._request_online_license(env_license)

            if online_license:
                # 在线验证成功，使用在线许可
                logger.info("在线许可验证成功")
                self.license_info = online_license
                self.is_valid = self._is_license_valid(online_license, 0)
            else:
                # 在线验证失败，使用环境变量许可（离线模式）
                logger.warning("在线许可验证失败，使用离线模式")
                self.license_info = env_license
                self.is_valid = self._is_license_valid(env_license, 3)

            if self.is_valid:
                logger.info(f"许可验证成功: {self.license_info.get('email')} 到期时间: {self.get_expires_at_formatted()}")
            else:
                logger.error("许可验证失败")

            return self.is_valid
        except Exception as e:
            logger.error(f"许可初始化失败: {e}")
            return False

    def is_feature_enabled(self, feature: str) -> bool:
        """检查功能是否启用"""
        if not self.is_valid or not self.license_info:
            return False

        # 这里可以根据不同的许可级别控制功能
        # 目前简单返回许可是否有效
        return True

    def get_license_info(self) -> Optional[Dict[str, Any]]:
        """获取许可信息"""
        return self.license_info if self.is_valid else None

    def get_expires_at_formatted(self) -> str:
        """获取格式化的过期时间"""
        if not self.license_info:
            return "未知"

        try:
            expires_at = self.license_info.get("expires_at", 0)
            return time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(expires_at))
        except Exception:
            return "未知"

    def get_email(self) -> str:
        """获取许可邮箱"""
        if not self.license_info:
            return "未知"
        return self.license_info.get("email", "未知")
