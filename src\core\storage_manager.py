from typing import Dict, Optional
from drivers._driver_factory import DriverFactory
from core.config_manager import ConfigManager
from core.log_manager import LogManager

logger = LogManager.get_logger(__name__)

class StorageManager:
    """存储管理器，用于统一管理所有存储实例"""

    _instance = None
    _storages: Dict[str, object] = {}

    def __new__(cls, *args, **kwargs):
        if cls._instance is None:
            cls._instance = super(StorageManager, cls).__new__(cls)
        return cls._instance

    def __init__(self, config_manager: ConfigManager):
        if not hasattr(self, 'initialized'):
            self.config_manager = config_manager
            self.initialized = True

    def get_storage(self, name: str) -> Optional[object]:
        """获取存储实例，如果不存在则创建

        Args:
            name: 存储名称

        Returns:
            Optional[object]: 存储实例，如果创建失败则返回 None
        """
        # 如果存储实例已存在且配置未改变，直接返回
        if name in self._storages:
            return self._storages[name]

        # 获取存储配置
        storage_config = self.config_manager.get_storage(name)
        if not storage_config:
            logger.error(f"存储配置不存在: {name}")
            return None

        # 创建存储实例
        storage = DriverFactory.create_driver(storage_config)
        if storage:
            self._storages[name] = storage
            return storage

        return None

    def reload_storage(self, name: str) -> Optional[object]:
        """重新加载存储实例

        Args:
            name: 存储名称

        Returns:
            Optional[object]: 存储实例，如果创建失败则返回 None
        """
        # 删除旧的存储实例
        if name in self._storages:
            del self._storages[name]

        # 重新创建存储实例
        return self.get_storage(name)

    def remove_storage(self, name: str) -> None:
        """移除存储实例

        Args:
            name: 存储名称
        """
        if name in self._storages:
            del self._storages[name]