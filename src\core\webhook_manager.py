import re
import logging
from typing import Dict, Any, Optional
from core.task_manager import Task<PERSON>anager
from core.config_manager import ConfigManager
from core.strm_generator import StrmGenerator
from drivers._driver_factory import DriverFactory

logger = logging.getLogger(__name__)


class WebhookManager:
    """Webhook 管理器，用于处理 webhook 相关的功能"""

    def __init__(self, config_manager: ConfigManager, task_manager: TaskManager):
        """初始化 Webhook 管理器

        Args:
            config_manager: 配置管理器实例
            task_manager: 任务管理器实例
        """
        self.config_manager = config_manager
        self.task_manager = task_manager

    def handle(
        self, method: str, args: Dict[str, Any], data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """处理 webhook 请求

        Args:
            method: 请求方法
            args: 请求参数
            data: webhook 请求数据
        Returns:
            Dict[str, Any]: 处理结果
        """
        try:
            # 记录 webhook 数据
            webhook_data = {
                "method": method,
                "args": args,
                "data": data,
            }
            logger.debug(f"收到 webhook 请求: {webhook_data}")

            # 处理一次性任务
            if data.get("event") == "a_task":
                return self._handle_a_task(data)

            # 处理一次性任务
            if data.get("event") == "qas_strm":
                return self._handle_qas_strm(data)

            # 处理 Emby 测试事件
            if data.get("Event") == "system.notificationtest":
                logger.info(
                    f"收到 Emby 测试事件，来自: {data['Server']['Name']} {data['Server']['Version']}"
                )
                return {"success": True, "message": "Emby Test", "data": data}

            # 处理 Emby 删除事件
            elif data.get("Event") == "library.deleted":
                return self._handle_emby_delete(data)

            # TODO: 处理其他事件

            return {"success": True, "message": "Webhook 处理成功"}
        except Exception as e:
            logger.error(f"处理 webhook 失败: {e}")
            return {"success": False, "message": f"处理 webhook 失败: {str(e)}"}

    def _handle_a_task(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """处理一次性任务

        Args:
            data: webhook 数据，包含任务参数
                {
                    "event": "a_task",
                    "task": {
                        "name": "任务名称",
                        "storage_path": "存储路径"
                    },
                    "strm": {
                        "media_ext": ["mp4", "mkv"],
                        "save_dir": "保存目录",
                        "url_encode": true,
                        "media_size": 100,
                        "copy_ext": ["srt", "ass"]
                    }
                }

        Returns:
            Dict[str, Any]: 处理结果
        """
        try:
            # 关键参数
            task_name = data.get("task", {}).get("name", "")
            # 获取任务配置
            task_config = self.config_manager.get_task(task_name)
            if not task_config:
                return {
                    "success": False,
                    "message": f"未找到对应的任务配置: {task_name}",
                }

            # 验证是否该任务的子路径
            if storage_path := data.get("task", {}).get("storage_path"):
                if not storage_path.startswith(task_config["storage_path"]):
                    return {
                        "success": False,
                        "message": f"{storage_path} 不是 {task_name} 任务的子目录",
                    }
            else:
                storage_path = task_config["storage_path"]

            # 获取 STRM 配置，如果未提供则使用默认配置
            strm_config = data.get("strm") or self.config_manager.get_settings()["strm"]

            # 获取存储配置
            storage_config = self.config_manager.get_storage(task_config["storage"])
            if not storage_config:
                return {
                    "success": False,
                    "message": f"未找到对应的存储配置: {task_config['storage']}",
                }

            # 创建存储实例
            storage = DriverFactory.create_driver(storage_config)
            if not storage:
                return {"success": False, "message": "创建存储实例失败"}

            # openlist 驱动强制刷新
            if storage.DRIVER_TYPE == "openlist":
                storage.ls_refresh = True

            # 创建 STRM 生成器
            generator = StrmGenerator(
                storage=storage,
                task_config=task_config,
                strm_config=strm_config,
            )

            # 运行任务
            generator.handle_path(storage_path)

            return {
                "success": True,
                "message": "任务执行成功",
                "task": {"name": task_name, "storage_path": storage_path},
            }
        except Exception as e:
            logger.error(f"处理一次性任务失败: {e}")
            return {"success": False, "message": f"处理一次性任务失败: {str(e)}"}

    def _handle_qas_strm(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """处理 QAS STRM 任务

        Args:
            data: webhook 数据，包含任务参数
                {
                    "event": "qas_strm",
                    "data": {
                        "strmtask": "taskname1,taskname2",
                        "savepath": "/tv/tvname/season01"
                        "xlist_path_fix": "/storage_mount_path:/quark_root_dir"
                    }
                }
        Returns:
            Dict[str, Any]: 处理结果
        """
        try:
            strmtasks = data["data"]["strmtask"].split(",")
            savepath = data["data"]["savepath"]
            xlist_path_fix = data["data"].get("xlist_path_fix")

            tasks_config = self.config_manager.get_tasks()
            for task in tasks_config:
                task_name = task["name"]
                if task_name not in strmtasks:
                    continue

                # 对驱动判断和处理
                driver_type = self.config_manager.get_storage(task["storage"])["driver"]
                if driver_type == "quark":
                    storage_path = savepath
                elif driver_type == "openlist":
                    if match := re.match(r"^(\/[^:]*):(\/[^:]*)$", xlist_path_fix):
                        # 存储挂载路径:夸克根文件夹
                        storage_mount_path = match.group(1)
                        quark_root_dir = match.group(2)
                        storage_path = f"{storage_mount_path}/{savepath.replace(quark_root_dir, '', 1).lstrip('/')}".replace(
                            "//", "/"
                        )
                    else:
                        storage_path = savepath
                else:
                    return {
                        "success": False,
                        "message": f"不支持任务 {task['name']} 的驱动类型 {driver_type}",
                    }

                # 判断是否在任务范围内
                if storage_path.startswith(task["storage_path"]):
                    return self._handle_a_task(
                        {
                            "task": {
                                "name": task_name,
                                "storage_path": storage_path,
                            },
                        }
                    )

            return {
                "success": False,
                "message": f"路径 {savepath} 不在任务 {strmtasks} 的范围内",
            }
        except Exception as e:
            return {
                "success": False,
                "message": f"触发任务失败: {str(e)}",
            }

    def _handle_emby_delete(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """处理 Emby 删除事件

        ** Emby 需要把 STRM 目录挂载在 /strm

        Args:
            data: Emby 删除事件 JSON 数据

        Returns:
            Dict[str, Any]: 处理结果
        """

        def get_item_by_name(name, list):
            for item in list:
                if item["name"] == name:
                    return item
            return None

        try:
            # 获取被删除的文件路径
            strm_path = data.get("Item", {}).get("Path", "")
            if not strm_path:
                return {"success": False, "message": "未找到文件路径"}

            # 从路径中提取任务名称
            # 例如：/strm/movie/冰下的鱼/冰下的鱼.(mp4).strm
            if match := re.match(r"^/strm/([^\/]+)(.*)", strm_path):
                task_name = match.group(1)  # movie
                file_path = re.sub(r"\.\((\w+)\)\.strm$", r".\1", match.group(2))
            else:
                logger.error(f"无效的文件路径格式: {strm_path}")
                return {"success": False, "message": "无效的文件路径格式"}

            # 获取任务配置
            tasks = self.config_manager.get_tasks()
            task_config = get_item_by_name(task_name, tasks)
            if not task_config:
                return {
                    "success": False,
                    "message": f"未找到对应的任务配置: {task_name}",
                }

            # 获取存储配置
            storages = self.config_manager.get_storages()
            storage_config = get_item_by_name(task_config["storage"], storages)
            if not storage_config:
                return {
                    "success": False,
                    "message": f"未找到对应的存储配置: {task_config['storage']}",
                }

            # 创建存储实例
            storage = DriverFactory.create_driver(storage_config)
            if not storage:
                return {"success": False, "message": "创建存储实例失败"}

            # 构建远程文件路径
            remote_path = f"{task_config['storage_path']}{file_path}"

            # 删除远程文件
            if storage.delete_file(remote_path):
                logger.info(f"成功删除远程文件: {remote_path}")
                return {
                    "success": True,
                    "message": "成功删除远程文件",
                    "remote_path": remote_path,
                }
            else:
                return {
                    "success": False,
                    "message": "删除远程文件失败",
                    "remote_path": remote_path,
                }
        except Exception as e:
            logger.error(f"处理 Emby 删除事件失败: {e}")
            return {"success": False, "message": f"处理 Emby 删除事件失败: {str(e)}"}
