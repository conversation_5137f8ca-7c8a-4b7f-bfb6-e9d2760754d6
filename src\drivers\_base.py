from abc import ABC, abstractmethod
from typing import List, Dict, Any, ClassVar


class BaseDriver(ABC):
    """远程存储驱动基类"""

    # 驱动类型名称
    DRIVER_TYPE: ClassVar[str] = ""

    # 驱动配置定义
    DRIVER_CONFIG: ClassVar[Dict[str, Dict[str, Any]]] = {}

    # 驱动级别提示信息
    DRIVER_TIPS: ClassVar[Dict[str, str]] = {
        "type": "info",  # info, warning, danger
        "message": "",  # 支持 HTML
    }

    @abstractmethod
    def list_files(self, path: str) -> List[Dict[str, Any]]:
        """列出指定路径下的所有文件

        Args:
            path: 要扫描的路径

        Returns:
            List[Dict]: 文件信息列表，每个文件必须包含 name, isdir, path, size, modified, created 信息，其他信息可选
        """
        pass

    @abstractmethod
    def delete_file(self, path: str) -> bool:
        """删除指定文件

        Args:
            path: 文件路径

        Returns:
            bool: 是否删除成功
        """
        pass

    @abstractmethod
    def rename_file(self, path: str, new_name: str) -> bool:
        """重命名文件或目录

        Args:
            path: 原文件路径
            new_name: 新文件名

        Returns:
            bool: 是否重命名成功
        """
        pass

    @abstractmethod
    def get_download_url(self, path: str, file_info: Dict[str, Any] = {}) -> str:
        """获取下载 URL

        Args:
            path: 文件路径
            file_info: 文件信息
        Returns:
            str: 下载 URL
        """
        pass

    @abstractmethod
    def get_strm_url(self, path: str, file_info: Dict[str, Any] = {}) -> str:
        """获取 strm URL

        Args:
            path: 文件路径
            file_info: 文件信息
        Returns:
            str: 下载 URL
        """
        pass

    @abstractmethod
    def get_file_data(self, path: str, file_info: Dict[str, Any] = {}) -> bytes:
        """获取文件二进制数据

        Args:
            path: 文件路径
            file_info: 文件信息
        Returns:
            bytes: 文件二进制数据
        """
        pass
