import os
import shutil
from core.log_manager import LogManager
from typing import List, Dict, Any
from datetime import datetime
from ._base import BaseDriver

logger = LogManager.get_logger(__name__)


class LocalDriver(BaseDriver):
    """本地文件系统驱动"""

    DRIVER_TYPE = "local"

    DRIVER_CONFIG = {
        "root_path": {
            "type": "string",
            "required": True,
            "label": "本地路径",
            "placeholder": "/mnt/media",
            "tip": "主机映射到容器的路径",
        }
    }

    DRIVER_TIPS = {
        "type": "info",
        "message": "使用本地文件系统驱动，需正确处理本容器和媒体应用的STRM路径映射，最简单的方式是都使用主机路径，如：<br><code>/mnt/media:/mnt/media</code>",
    }

    def __init__(self, root_path: str):
        """初始化本地文件系统驱动

        Args:
            root_path: 本地文件系统根路径
        """
        self.name = None
        self.root_path = os.path.abspath(root_path)
        if not os.path.exists(self.root_path):
            raise ValueError(f"路径不存在: {self.root_path}")

    def list_files(self, path: str) -> List[Dict[str, Any]]:
        """列出指定路径下的所有文件

        Args:
            path: 要扫描的路径

        Returns:
            List[Dict]: 文件信息列表，每个文件包含 name, path, is_dir 等信息
        """
        try:
            full_path = os.path.join(self.root_path, path.lstrip("/"))
            if not os.path.exists(full_path):
                logger.error(f"路径不存在: {full_path}")
                return []

            files = []
            for item in os.listdir(full_path):
                item_path = os.path.join(full_path, item)
                stat = os.stat(item_path)
                isdir = os.path.isdir(item_path)

                files.append(
                    {
                        "name": item,
                        "isdir": isdir,
                        "path": item_path,
                        "size": stat.st_size if not isdir else "",
                        "modified": stat.st_mtime,
                        "created": stat.st_birthtime,
                        "type": None,  # 本地文件系统不需要content_type
                    }
                )

            return files
        except Exception as e:
            logger.error(f"列出文件失败: {e}")
            return []

    def delete_file(self, path: str) -> bool:
        """删除指定文件

        Args:
            path: 文件路径

        Returns:
            bool: 是否删除成功
        """
        try:
            full_path = os.path.join(self.root_path, path.lstrip("/"))
            if os.path.isfile(full_path):
                os.remove(full_path)
            elif os.path.isdir(full_path):
                shutil.rmtree(full_path)
            return True
        except Exception as e:
            logger.error(f"删除文件失败: {e}")
            return False

    def rename_file(self, path: str, new_name: str) -> bool:
        """重命名文件或目录

        Args:
            path: 原文件路径
            new_name: 新文件名

        Returns:
            bool: 是否重命名成功
        """
        try:
            old_path = os.path.join(self.root_path, path.lstrip("/"))
            new_path = os.path.join(os.path.dirname(old_path), new_name)
            os.rename(old_path, new_path)
            return True
        except Exception as e:
            logger.error(f"重命名文件失败: {e}")
            return False

    def get_download_url(self, path: str, file_info: Dict[str, Any] = {}) -> str:
        """获取下载 URL（本地文件系统返回绝对路径）

        Args:
            path: 文件路径
            file_info: 文件信息

        Returns:
            str: 文件的绝对路径
        """
        return os.path.join(self.root_path, path.lstrip("/")).replace("\\", "/")

    def get_strm_url(self, path: str, file_info: Dict[str, Any] = {}) -> str:
        """获取 strm URL

        Args:
            path: 文件路径
            file_info: 文件信息
        """
        return self.get_download_url(path, file_info)

    def get_file_data(self, path: str, file_info: Dict[str, Any] = {}) -> bytes:
        """获取文件二进制数据

        Args:
            path: 文件路径
            file_info: 文件信息
        Returns:
            bytes: 文件二进制数据
        """
        try:
            file_path = self.get_download_url(path, file_info)
            with open(file_path, "rb") as f:
                return f.read()
        except Exception as e:
            logger.error(f"读取文件数据失败: {e}")
            return b""
