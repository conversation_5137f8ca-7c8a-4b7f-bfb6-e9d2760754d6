import os
import requests
import traceback
from typing import Dict, Any, List, Optional
from ._base import BaseDriver
from core.log_manager import LogManager

logger = LogManager.get_logger(__name__)


class QuarkDriver(BaseDriver):
    """夸克网盘驱动类"""

    DRIVER_TYPE = "quark"

    DRIVER_CONFIG = {
        "cookie": {
            "type": "string",
            "required": True,
            "label": "Cookie",
            "tip": "从浏览器 F12 获取的 Cookie",
        }
    }

    DRIVER_TIPS = {
        "type": "info",
        "message": "使用夸克网盘 PC API 访问<br>由 SmartStrm 提供 STRM 解析",
    }

    BASE_URL = "https://drive-pc.quark.cn"
    USER_AGENT = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) quark-cloud-drive/3.19.0 Chrome/112.0.5615.165 Electron/******** Safari/537.36 Channel/pckk_other_ch"

    def __init__(self, cookie: str):
        """初始化夸克网盘驱动

        Args:
            cookie: 夸克网盘的 Cookie
        """
        self.name = None
        self.cookie = cookie.strip()
        self.path_fids = {"/": "0"}
        self.headers = {
            "cookie": self.cookie,
            "content-type": "application/json",
            "user-agent": self.USER_AGENT,
        }

    def _send_request(
        self, method: str, url: str, **kwargs
    ) -> Optional[requests.Response]:
        """发送请求到夸克网盘 API

        Args:
            method: 请求方法
            url: 请求地址
            **kwargs: 请求参数

        Returns:
            Optional[requests.Response]: 响应对象，如果请求失败则返回 None
        """
        try:
            if "headers" in kwargs:
                headers = kwargs["headers"]
                del kwargs["headers"]
            else:
                headers = self.headers
            response = requests.request(method, url, headers=headers, **kwargs)
            return response
        except Exception as e:
            logger.error(f"请求夸克网盘 API 失败: {e}")
            return None

    def _get_fid_by_path(self, path: str) -> str:
        """获取指定路径的文件夹 ID

        Args:
            path: 文件夹路径

        Returns:
            str: 文件夹 ID
        """
        if path in self.path_fids:
            return self.path_fids[path]

        url = f"{self.BASE_URL}/1/clouddrive/file/info/path_list"
        querystring = {"pr": "ucpro", "fr": "pc"}
        payload = {"file_path": [path], "namespace": "0"}
        response = self._send_request("POST", url, json=payload, params=querystring)
        if response and response.status_code == 200:
            data = response.json()
            if data["code"] == 0 and data["data"]:
                fid = data["data"][0]["fid"]
                self.path_fids[path] = fid
                return fid
        return ""

    def list_files(self, path: str = "/") -> List[Dict[str, Any]]:
        """列出指定路径下的文件

        Args:
            path: 要列出的路径

        Returns:
            List[Dict[str, Any]]: 文件列表，每个文件包含 name, isdir, path, size, modified 等信息
        """
        try:
            pdir_fid = self._get_fid_by_path(path)
            if not pdir_fid:
                logger.error(f"获取文件夹 ID 失败: {path}")
                return []
            url = f"{self.BASE_URL}/1/clouddrive/file/sort"
            querystring = {
                "pr": "ucpro",
                "fr": "pc",
                "uc_param_str": "",
                "pdir_fid": pdir_fid,
                "_page": 1,
                "_size": 200,
                "_fetch_total": 1,
                "_fetch_sub_dirs": 0,
                "_sort": "file_type:asc,updated_at:desc",
                "_fetch_full_path": 1,
            }
            response = self._send_request("GET", url, params=querystring)
            if not response or response.status_code != 200:
                return []

            data = response.json()
            if data["code"] != 0:
                return []

            files = []
            for item in data["data"]["list"]:
                # 缓存 fid ，减少后续请求
                item_path = os.path.join(path, item["file_name"]).replace("\\", "/")
                self.path_fids[item_path] = item["fid"]
                files.append(
                    {
                        "name": item["file_name"],
                        "isdir": item["dir"],
                        "path": item_path,
                        "size": item["size"] if not item["dir"] else "",
                        "modified": item["updated_at"] / 1000,
                        "created": item["created_at"] / 1000,
                        # 可选参数
                        "type": item["file_type"],
                        "fid": item["fid"],
                    }
                )
            return files
        except Exception as e:
            logger.error(f"列出文件失败: {e}")
            traceback.print_exc()
            return []

    def delete_file(self, path: str) -> bool:
        """删除指定文件

        Args:
            path: 文件路径

        Returns:
            bool: 是否删除成功
        """
        try:
            if path in self.path_fids:
                fid = self.path_fids[path]
            else:
                file_name = os.path.basename(path)
                files = self.list_files(os.path.dirname(path))
                file_info = next((f for f in files if f["name"] == file_name), None)
                if not file_info:
                    return False
                fid = file_info["fid"]

            url = f"{self.BASE_URL}/1/clouddrive/file/delete"
            querystring = {"pr": "ucpro", "fr": "pc", "uc_param_str": ""}
            payload = {
                "action_type": 2,
                "filelist": [fid],
                "exclude_fids": [],
            }
            response = self._send_request("POST", url, json=payload, params=querystring)
            if not response or response.status_code != 200:
                return False

            data = response.json()
            return data["code"] == 0
        except Exception as e:
            logger.error(f"删除文件失败: {e}")
            return False

    def rename_file(self, path: str, new_name: str) -> bool:
        """重命名文件或目录

        Args:
            path: 原文件路径
            new_name: 新文件名

        Returns:
            bool: 是否重命名成功
        """
        try:
            if path in self.path_fids:
                fid = self.path_fids[path]
            else:
                file_name = os.path.basename(path)
                files = self.list_files(os.path.dirname(path))
                file_info = next((f for f in files if f["name"] == file_name), None)
                if not file_info:
                    return False
                fid = file_info["fid"]

            url = f"{self.BASE_URL}/1/clouddrive/file/rename"
            querystring = {"pr": "ucpro", "fr": "pc", "uc_param_str": ""}
            payload = {"fid": fid, "file_name": new_name}
            response = self._send_request("POST", url, json=payload, params=querystring)
            if not response or response.status_code != 200:
                return False

            data = response.json()
            return data["code"] == 0
        except Exception as e:
            logger.error(f"重命名文件失败: {e}")
            return False

    def get_download_url(self, path: str, file_info: Dict[str, Any] = {}) -> str:
        """获取下载 URL

        Args:
            path: 文件路径
            file_info: 文件信息

        Returns:
            str: 下载 URL
        """
        # 判断是否为视频文件
        video_extensions = [".mp4", ".mkv", ".avi", ".mov", ".3gp", ".flv"]
        is_video = any(path.lower().endswith(ext) for ext in video_extensions)
        if is_video:
            # 使用视频播放API获取直链
            try:
                if path in self.path_fids:
                    fid = self.path_fids[path]
                else:
                    file_name = os.path.basename(path)
                    files = self.list_files(os.path.dirname(path))
                    file_info = next((f for f in files if f["name"] == file_name), None)
                    if not file_info:
                        return ""
                    fid = file_info["fid"]

                url = f"{self.BASE_URL}/1/clouddrive/file/v2/play/project"
                querystring = {"pr": "ucpro", "fr": "pc", "uc_param_str": ""}
                payload = {
                    "fid": fid,
                    "resolutions": "low,normal,high,super,2k,4k",
                    "supports": "fmp4_av,m3u8,dolby_vision",
                }
                response = self._send_request(
                    "POST", url, json=payload, params=querystring
                )
                if response.status_code == 200:
                    data = response.json()
                    if data["code"] == 0 and data["data"]:
                        return data["data"]["video_list"][0]["video_info"]["url"]
            except Exception as e:
                logger.error(f"获取视频转码直链失败: {e}")
        return ""

    def get_strm_url(self, path: str, file_info: Dict[str, Any] = {}) -> str:
        """获取 strm URL

        Args:
            path: 文件路径
            file_info: 文件信息
        """
        return f"SMARTSTRM_BASE/smartstrm/{self.name}/{path}".replace("//", "/")

    def _get_download_http(self, path: str, file_info: Dict[str, Any] = {}) -> dict:
        """获取下载 HTTP 请求

        Args:
            path: 文件路径
            file_info: 文件信息

        Returns:
            str: 下载 HTTP 请求
        """
        try:
            if path in self.path_fids:
                fid = self.path_fids[path]
            else:
                file_name = os.path.basename(path)
                files = self.list_files(os.path.dirname(path))
                file_info = next((f for f in files if f["name"] == file_name), None)
                if not file_info:
                    return {}
                fid = file_info["fid"]

            url = f"{self.BASE_URL}/1/clouddrive/file/download"
            querystring = {"pr": "ucpro", "fr": "pc", "uc_param_str": ""}
            payload = {"fids": [fid], "speedup_session": ""}
            response = self._send_request("POST", url, json=payload, params=querystring)
            if not response or response.status_code != 200:
                logger.error(f"获取下载 URL 失败: {response.text}")
                return {}

            data = response.json()
            if data["code"] == 0 and data["data"]:
                set_cookie = response.cookies.get_dict()
                cookie_str = "; ".join(
                    [f"{key}={value}" for key, value in set_cookie.items()]
                )
                return {
                    "url": data["data"][0]["download_url"],
                    "headers": {
                        "user-agent": self.USER_AGENT,
                        "cookie": f"{self.cookie}; {cookie_str}",
                    },
                }
            return {}
        except Exception as e:
            logger.error(f"获取下载 URL 失败: {e}")
            return {}

    def get_file_data(self, path: str, file_info: Dict[str, Any] = {}) -> bytes:
        """获取文件二进制数据

        Args:
            path: 文件路径
            file_info: 文件信息
        Returns:
            bytes: 文件二进制数据
        """
        try:
            download_info = self._get_download_http(path, file_info)
            if not download_info:
                logger.error(f"获取下载信息失败: {path}")
                return b""

            response = requests.get(
                download_info["url"],
                headers=download_info["headers"]
            )
            response.raise_for_status()
            return response.content
        except Exception as e:
            logger.error(f"获取文件数据失败: {e}")
            return b""
